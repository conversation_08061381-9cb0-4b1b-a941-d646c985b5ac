"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function for generating crew member initials\nconst getCrewInitials = (crewName)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!crewName) return \"??\";\n    const names = crewName.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Function to create columns for the crew seatime report\nconst createCrewSeatimeColumns = function(bp, getVesselWithIcon) {\n    let vessels = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Crew member name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 17\n                }, _this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                const crewContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"leading-tight truncate font-medium hover:text-curious-blue-400\",\n                    children: item.crewName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 21\n                }, _this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col py-2.5 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"desktop:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.crewName)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 33\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 29\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        crewContent,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.vesselName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 37\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.primaryDuty\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 37\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 33\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 29\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 25\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden desktop:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                        size: \"sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: getCrewInitials(item.crewName)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 37\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 33\n                                    }, _this),\n                                    crewContent\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 29\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 25\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.crewName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.crewName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Find the actual vessel by name from the vessels list\n                const actualVessel = vessels.find((vessel)=>vessel.title === item.vesselName);\n                if (actualVessel) {\n                    // Use the actual vessel data with proper ID\n                    const vesselWithIcon = getVesselWithIcon(actualVessel.id, actualVessel);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        vesselId: actualVessel.id,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 25\n                    }, _this);\n                } else {\n                    // Fallback for vessels not found in the list\n                    const vesselForIcon = {\n                        id: 0,\n                        title: item.vesselName\n                    };\n                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 25\n                    }, _this);\n                }\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Duty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hover:text-curious-blue-400\",\n                    children: item.primaryDuty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.primaryDuty) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.primaryDuty) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"loginTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Signed in\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/M/YY HH:mm\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.loginTime) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.loginTime) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"logoutTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Signed out\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/M/YY HH:mm\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.logoutTime) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.logoutTime) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"loggedDuration\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Time spent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        item.loggedDuration.hours != 0 ? \"\".concat(item.loggedDuration.hours, \"h, \") : \"\",\n                        item.loggedDuration.minutes,\n                        \"m\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.totalLoggedMinutes) || 0;\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.totalLoggedMinutes) || 0;\n                return valueA - valueB;\n            }\n        }\n    ]);\n};\nconst CrewSeatimeReportActions = (param)=>{\n    let { onDownloadCsv, onDownloadPdf } = param;\n    _s();\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_15__.useSidebar)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_16__.SealogsCogIcon, {\n                    size: 36\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 299,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                side: isMobile ? \"bottom\" : \"right\",\n                align: isMobile ? \"end\" : \"start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-input flex flex-col items-center justify-center py-[9px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                            variant: \"backButton\",\n                            onClick: ()=>router.push(\"/reporting\"),\n                            children: \"Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 21\n                        }, undefined),\n                        onDownloadPdf && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadPdf,\n                            children: \"Download PDF\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 25\n                        }, undefined),\n                        onDownloadCsv && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadCsv,\n                            children: \"Download CSV\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 302,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n        lineNumber: 298,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewSeatimeReportActions, \"2jIoXD9G8OZZK/Hd0W/SlED0TzQ=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_15__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = CrewSeatimeReportActions;\nfunction NewCrewSeatimeReport() {\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__.useVesselIconData)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: null,\n        endDate: null\n    });\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load vessels for vessel lookup by name\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                const activeVessels = queryVesselResponse.readVessels.nodes.filter((vessel)=>!vessel.archived);\n                setVessels(activeVessels);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // Load vessels on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    }, [\n        queryVessels\n    ]);\n    // Create columns with access to bp, vessel icon data, and vessels list\n    const columns = createCrewSeatimeColumns(bp, getVesselWithIcon, vessels);\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{},\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews && selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n        }\n        if (selectedDuties && selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n        }\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    // Auto-generate report on page load if no filters are actively selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hasActiveFilters = selectedCrews.length > 0 || selectedDuties.length > 0 || selectedVessels.length > 0 || dateRange.startDate !== null && dateRange.endDate !== null;\n        if (!hasActiveFilters) {\n            generateReport();\n        }\n    }, [\n        generateReport\n    ]) // Run when generateReport is available\n    ;\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        if (reportMode === \"detailed\") {\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CrewSeatimeReportActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 651,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 661,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 660,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(NewCrewSeatimeReport, \"osTjsaEMmu8Ltm+BMoi+VUrnFf0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c1 = NewCrewSeatimeReport;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewSeatimeReportActions\");\n$RefreshReg$(_c1, \"NewCrewSeatimeReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});