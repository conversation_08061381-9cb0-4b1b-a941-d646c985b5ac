"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _export_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./export-button */ \"(app-pages-browser)/./src/app/ui/reporting/export-button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction NewCrewSeatimeReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        startDate: null,\n        endDate: null\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readCrewMembers_LogBookEntrySections_nodes, _data_readCrewMembers_LogBookEntrySections;\n            console.log(\"✅ GraphQL query completed successfully\");\n            console.log(\"\\uD83D\\uDCE6 Raw data received:\", data);\n            console.log(\"\\uD83D\\uDCCA Nodes count:\", (data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections_nodes = _data_readCrewMembers_LogBookEntrySections.nodes) === null || _data_readCrewMembers_LogBookEntrySections_nodes === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections_nodes.length) || 0);\n        },\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD0D generateReport called\");\n        console.log(\"\\uD83D\\uDCCA Current state:\", {\n            selectedCrews,\n            selectedDuties,\n            selectedVessels,\n            dateRange\n        });\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews && selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n            console.log(\"\\uD83D\\uDC65 Added crew filter:\", filter[\"crewMemberID\"]);\n        }\n        if (selectedDuties && selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n            console.log(\"\\uD83D\\uDCBC Added duty filter:\", filter[\"dutyPerformedID\"]);\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n            console.log(\"\\uD83D\\uDEA2 Added vessel filter:\", logBookFilter.vehicleID);\n        }\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n            console.log(\"\\uD83D\\uDCC5 Added date filter:\", logBookFilter.startDate);\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n            console.log(\"\\uD83D\\uDCD6 Added logBookEntry filter:\", filter[\"logBookEntry\"]);\n        }\n        console.log(\"\\uD83C\\uDFAF Final filter object:\", filter);\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        console.log(\"\\uD83D\\uDD04 Processing reportData useMemo\");\n        console.log(\"\\uD83D\\uDCCA called:\", called, \"loading:\", loading);\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        console.log(\"\\uD83D\\uDCE6 Raw reportData length:\", reportData.length);\n        console.log(\"\\uD83D\\uDCE6 Raw reportData sample:\", reportData.slice(0, 2));\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        console.log(\"\\uD83D\\uDD0D Filtered data (punchOut !== null) length:\", filteredData.length);\n        console.log(\"\\uD83D\\uDD0D Filtered data sample:\", filteredData.slice(0, 2));\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        console.log(\"\\uD83D\\uDCCB Report items created:\", reportItems.length);\n        console.log(\"\\uD83D\\uDCCB Report items sample:\", reportItems.slice(0, 2));\n        console.log(\"\\uD83C\\uDFAF Report mode:\", reportMode);\n        if (reportMode === \"detailed\") {\n            console.log(\"✅ Returning detailed report with\", reportItems.length, \"items\");\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        console.log(\"✅ Returning summarized report with\", summarizedReportItems.length, \"items\");\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 370,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onChange: handleFilterOnChange,\n                            onClick: generateReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_export_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            onDownloadPdf: downloadPdf,\n                            onDownloadCsv: downloadCsv\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Crew member name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Vessel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Duty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Time spent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                        isLoading: called && loading,\n                                        reportData: reportData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 382,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewCrewSeatimeReport, \"86RVhtuoEgMHcF3datVCD9Vt9QA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c = NewCrewSeatimeReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 426,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 425,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"No Data Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 436,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 435,\n            columnNumber: 13\n        }, this);\n    }\n    return reportData.map((element, index)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            className: \"group border-b  hover: \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block ml-3\",\n                        children: element.crewName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.primaryDuty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.loginTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.logoutTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: [\n                            element.loggedDuration.hours != 0 ? \"\".concat(element.loggedDuration.hours, \"h, \") : \"\",\n                            element.loggedDuration.minutes,\n                            \"m\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, \"\".concat(element.crewID, \"-\").concat(index), true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 445,\n            columnNumber: 13\n        }, this);\n    });\n}\n_c1 = TableContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewCrewSeatimeReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});