"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/ui/maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ var _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions for generating initials (similar to maintenance list)\nconst getCrewInitials = (assignedTo)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!assignedTo) return \"??\";\n    const names = assignedTo.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Helper function to extract status text using the exact same logic as StatusBadge\n// This ensures consistency between visual display and exported data\nconst getStatusText = (isOverDue)=>{\n    let statusText = \"\";\n    if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(isOverDue.status)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) === \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Upcoming\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) !== \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    }\n    return statusText || \"\";\n};\n// Helper function to create a compatible MaintenanceCheck object for StatusBadge\nconst createMaintenanceCheckForBadge = (reportItem)=>{\n    return {\n        id: 0,\n        assignedTo: {\n            id: 0,\n            name: \"\"\n        },\n        basicComponent: {\n            id: 0,\n            title: null\n        },\n        inventory: {\n            id: 0,\n            item: null\n        },\n        status: reportItem.status || \"\",\n        recurringID: 0,\n        name: reportItem.taskName,\n        created: \"\",\n        severity: \"\",\n        isOverDue: reportItem.dueStatus,\n        comments: null,\n        workOrderNumber: null,\n        startDate: \"\",\n        expires: null,\n        maintenanceCategoryID: 0\n    };\n};\n// Helper function to get status color classes (similar to maintenance list)\nconst getStatusColorClasses = (status)=>{\n    switch(status){\n        case \"High\":\n            return \"text-destructive hover:text-cinnabar-800\";\n        case \"Upcoming\":\n            return \"text-warning hover:text-fire-bush-500\";\n        default:\n            return \"hover:text-curious-blue-400\";\n    }\n};\n// Function to create columns (will be called inside component to access bp, vessel data, and vessels list)\nconst createMaintenanceReportColumns = function(bp, getVesselWithIcon) {\n    let vessels = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Task Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 17\n                }, _this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus;\n                const item = row.original;\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const taskContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"leading-tight truncate font-medium\", getStatusColorClasses(overDueStatus)),\n                    children: item.taskName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 21\n                }, _this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"desktop:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 37\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 33\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        taskContent,\n                                        item.inventoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-curious-blue-400 text-sm\",\n                                                children: item.inventoryName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 41\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 37\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 29\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 25\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden tablet-sm:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: taskContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 33\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 29\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 25\n                        }, _this)\n                    ]\n                }, void 0, true);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.taskName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.taskName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventoryName\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.inventoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hover:text-curious-blue-400\",\n                        children: item.inventoryName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 29\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.inventoryName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.inventoryName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Location\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Find the actual vessel by name from the vessels list\n                const actualVessel = vessels.find((vessel)=>vessel.title === item.vesselName);\n                if (actualVessel) {\n                    // Use the actual vessel data with proper ID\n                    const vesselWithIcon = getVesselWithIcon(actualVessel.id, actualVessel);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        vesselId: actualVessel.id,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 25\n                    }, _this);\n                } else {\n                    // Fallback for vessels not found in the list\n                    const vesselForIcon = {\n                        id: 0,\n                        title: item.vesselName\n                    };\n                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 25\n                    }, _this);\n                }\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"assignedTo\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 17\n                }, _this);\n            },\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        mobileClickable: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                mobileClickable: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 41\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 37\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 33\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                children: item.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 33\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.assignedTo) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.assignedTo) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 17\n                }, _this);\n            },\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.status || \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.status) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.status) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"dueDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"dueStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellClassName: \"px-2.5\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus, _item_dueStatus1;\n                const item = row.original;\n                const maintenanceCheck = createMaintenanceCheckForBadge(item);\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 28\n                    }, _this);\n                }\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const overDueDays = (_item_dueStatus1 = item.dueStatus) === null || _item_dueStatus1 === void 0 ? void 0 : _item_dueStatus1.days;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n                        children: overDueDays || \"Overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 33\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 33\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_dueStatus, _rowA_original, _rowB_original_dueStatus, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_dueStatus = _rowA_original.dueStatus) === null || _rowA_original_dueStatus === void 0 ? void 0 : _rowA_original_dueStatus.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_dueStatus = _rowB_original.dueStatus) === null || _rowB_original_dueStatus === void 0 ? void 0 : _rowB_original_dueStatus.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n};\n// Row status evaluator for maintenance report (similar to maintenance list)\nconst getMaintenanceReportRowStatus = (reportItem)=>{\n    var _reportItem_dueStatus;\n    // Skip completed, archived, or draft tasks\n    if (reportItem.status === \"Completed\" || reportItem.status === \"Save_As_Draft\") {\n        return \"normal\";\n    }\n    const overDueStatus = (_reportItem_dueStatus = reportItem.dueStatus) === null || _reportItem_dueStatus === void 0 ? void 0 : _reportItem_dueStatus.status;\n    // Use the pre-calculated status values from the system\n    switch(overDueStatus){\n        case \"High\":\n            return \"overdue\" // Red highlighting\n            ;\n        case \"Upcoming\":\n            return \"upcoming\" // Orange highlighting\n            ;\n        case \"Medium\":\n        case \"Open\":\n        default:\n            return \"normal\" // No highlighting\n            ;\n    }\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load vessels for vessel lookup by name\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_18__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                const activeVessels = queryVesselResponse.readVessels.nodes.filter((vessel)=>!vessel.archived);\n                setVessels(activeVessels);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // Load vessels on component mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    }, [\n        queryVessels\n    ]);\n    // Create columns with access to bp, vessel icon data, and vessels list\n    const columns = createMaintenanceReportColumns(bp, getVesselWithIcon, vessels);\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readComponentMaintenanceChecks_nodes, _data_readComponentMaintenanceChecks;\n            // Log unique status values to understand what's available\n            const statusValues = new Set();\n            data === null || data === void 0 ? void 0 : (_data_readComponentMaintenanceChecks = data.readComponentMaintenanceChecks) === null || _data_readComponentMaintenanceChecks === void 0 ? void 0 : (_data_readComponentMaintenanceChecks_nodes = _data_readComponentMaintenanceChecks.nodes) === null || _data_readComponentMaintenanceChecks_nodes === void 0 ? void 0 : _data_readComponentMaintenanceChecks_nodes.forEach((node)=>{\n                if (node.status) {\n                    statusValues.add(node.status);\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"❌ GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error:\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>+item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: +category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: +crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                getStatusText(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                getStatusText(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__.MaintenanceReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 672,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    rowStatus: getMaintenanceReportRowStatus,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 682,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 681,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"bXSSC/NOcU8MfvYORn+MZ1Yz9d0=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});