"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _export_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./export-button */ \"(app-pages-browser)/./src/app/ui/reporting/export-button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction NewCrewSeatimeReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        startDate: null,\n        endDate: null\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{},\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews && selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n        }\n        if (selectedDuties && selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n        }\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        if (reportMode === \"detailed\") {\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 338,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onChange: handleFilterOnChange,\n                            onClick: generateReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_export_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            onDownloadPdf: downloadPdf,\n                            onDownloadCsv: downloadCsv\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Crew member name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Vessel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Duty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Time spent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                        isLoading: called && loading,\n                                        reportData: reportData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 350,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewCrewSeatimeReport, \"86RVhtuoEgMHcF3datVCD9Vt9QA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c = NewCrewSeatimeReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 394,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 393,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"No Data Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 404,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 403,\n            columnNumber: 13\n        }, this);\n    }\n    return reportData.map((element, index)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            className: \"group border-b  hover: \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block ml-3\",\n                        children: element.crewName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.primaryDuty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 422,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.loginTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.logoutTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: [\n                            element.loggedDuration.hours != 0 ? \"\".concat(element.loggedDuration.hours, \"h, \") : \"\",\n                            element.loggedDuration.minutes,\n                            \"m\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, \"\".concat(element.crewID, \"-\").concat(index), true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 413,\n            columnNumber: 13\n        }, this);\n    });\n}\n_c1 = TableContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewCrewSeatimeReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});