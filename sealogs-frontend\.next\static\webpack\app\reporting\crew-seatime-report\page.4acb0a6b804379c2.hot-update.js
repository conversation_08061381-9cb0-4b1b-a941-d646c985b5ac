"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Helper function for generating crew member initials\nconst getCrewInitials = (crewName)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!crewName) return \"??\";\n    const names = crewName.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nfunction NewCrewSeatimeReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: null,\n        endDate: null\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{},\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews && selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n        }\n        if (selectedDuties && selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n        }\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        if (reportMode === \"detailed\") {\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 349,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Filter, {\n                            onChange: handleFilterOnChange,\n                            onClick: generateReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExportButton, {\n                            onDownloadPdf: downloadPdf,\n                            onDownloadCsv: downloadCsv\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Crew member name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Vessel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Duty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Signed in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Signed out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Time spent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                        isLoading: called && loading,\n                                        reportData: reportData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 361,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewCrewSeatimeReport, \"86RVhtuoEgMHcF3datVCD9Vt9QA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery\n    ];\n});\n_c = NewCrewSeatimeReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 405,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 404,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"No Data Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 415,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 414,\n            columnNumber: 13\n        }, this);\n    }\n    return reportData.map((element, index)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            className: \"group border-b  hover: \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block ml-3\",\n                        children: element.crewName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.primaryDuty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(element.loginTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(element.logoutTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: [\n                            element.loggedDuration.hours != 0 ? \"\".concat(element.loggedDuration.hours, \"h, \") : \"\",\n                            element.loggedDuration.minutes,\n                            \"m\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, \"\".concat(element.crewID, \"-\").concat(index), true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 424,\n            columnNumber: 13\n        }, this);\n    });\n}\n_c1 = TableContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewCrewSeatimeReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});