"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function for generating crew member initials\nconst getCrewInitials = (crewName)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!crewName) return \"??\";\n    const names = crewName.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Function to create columns for the crew seatime report\nconst createCrewSeatimeColumns = function(bp, getVesselWithIcon) {\n    let vessels = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"crewName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Crew member name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 17\n                }, _this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                const crewContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"leading-tight truncate font-medium hover:text-curious-blue-400\",\n                    children: item.crewName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 21\n                }, _this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col py-2.5 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"desktop:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.crewName)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 33\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 29\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        crewContent,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.vesselName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 37\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.primaryDuty\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 37\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 33\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 29\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 25\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden desktop:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                        size: \"sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: getCrewInitials(item.crewName)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 37\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 33\n                                    }, _this),\n                                    crewContent\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 29\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 25\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.crewName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.crewName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Find the actual vessel by name from the vessels list\n                const actualVessel = vessels.find((vessel)=>vessel.title === item.vesselName);\n                if (actualVessel) {\n                    // Use the actual vessel data with proper ID\n                    const vesselWithIcon = getVesselWithIcon(actualVessel.id, actualVessel);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        vesselId: actualVessel.id,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 25\n                    }, _this);\n                } else {\n                    // Fallback for vessels not found in the list\n                    const vesselForIcon = {\n                        id: 0,\n                        title: item.vesselName\n                    };\n                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 25\n                    }, _this);\n                }\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Duty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hover:text-curious-blue-400\",\n                    children: item.primaryDuty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.primaryDuty) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.primaryDuty) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"loginTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Signed in\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/M/YY HH:mm\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.loginTime) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.loginTime) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"logoutTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Signed out\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/M/YY HH:mm\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.logoutTime) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.logoutTime) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"loggedDuration\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Time spent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        item.loggedDuration.hours != 0 ? \"\".concat(item.loggedDuration.hours, \"h, \") : \"\",\n                        item.loggedDuration.minutes,\n                        \"m\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.totalLoggedMinutes) || 0;\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.totalLoggedMinutes) || 0;\n                return valueA - valueB;\n            }\n        }\n    ]);\n};\nfunction NewCrewSeatimeReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__.useVesselIconData)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: null,\n        endDate: null\n    });\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load vessels for vessel lookup by name\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                const activeVessels = queryVesselResponse.readVessels.nodes.filter((vessel)=>!vessel.archived);\n                setVessels(activeVessels);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // Load vessels on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    }, [\n        queryVessels\n    ]);\n    // Auto-generate report on page load if no filters are actively selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hasActiveFilters = selectedCrews.length > 0 || selectedDuties.length > 0 || selectedVessels.length > 0 || dateRange.startDate !== null && dateRange.endDate !== null;\n        if (!hasActiveFilters) {\n            generateReport();\n        }\n    }, []) // Only run on mount\n    ;\n    // Create columns with access to bp, vessel icon data, and vessels list\n    const columns = createCrewSeatimeColumns(bp, getVesselWithIcon, vessels);\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{},\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews && selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n        }\n        if (selectedDuties && selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n        }\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        if (reportMode === \"detailed\") {\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CrewSeatimeReportActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 604,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 614,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 613,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewCrewSeatimeReport, \"9cE7SRlKenMH8i/rWNebmfHLqZI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\n_c = NewCrewSeatimeReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 637,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 636,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"No Data Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 647,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 646,\n            columnNumber: 13\n        }, this);\n    }\n    return reportData.map((element, index)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            className: \"group border-b  hover: \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block ml-3\",\n                        children: element.crewName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 662,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.primaryDuty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(element.loginTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(element.logoutTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 673,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: [\n                            element.loggedDuration.hours != 0 ? \"\".concat(element.loggedDuration.hours, \"h, \") : \"\",\n                            element.loggedDuration.minutes,\n                            \"m\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 678,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, \"\".concat(element.crewID, \"-\").concat(index), true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 656,\n            columnNumber: 13\n        }, this);\n    });\n}\n_c1 = TableContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewCrewSeatimeReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});