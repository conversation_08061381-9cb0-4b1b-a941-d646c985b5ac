'use client'

import React, { useMemo, useState } from 'react'
import { GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES } from '@/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES'
import { useLazyQuery } from '@apollo/client'
import { isOverDueTask } from '@/app/lib/actions'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { createColumns, DataTable, RowStatus } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { StatusBadge } from '@/app/ui/maintenance/list/list'
import { MaintenanceReportFilterActions } from '@/components/filter/components/maintenance-report-actions'
import {
    <PERSON>Header,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ontent,
    Tooltip<PERSON>rigger,
} from '@/components/ui'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'

import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { cn } from '@/app/lib/utils'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { VesselLocationDisplay } from '@/components/ui/vessel-location-display'
import { VESSEL_BRIEF_LIST } from '@/app/lib/graphQL/query'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

type FilterType = 'dateRange' | 'vessels' | 'category' | 'status' | 'member'
interface IFilter {
    type: FilterType
    data: any
}

interface IReportItem {
    taskName: string
    inventoryName?: string
    vesselName?: string
    assignedTo?: string
    status?: string
    dueDate?: Date
    dueStatus: any
}

// Helper functions for generating initials (similar to maintenance list)
const getCrewInitials = (assignedTo?: string): string => {
    if (!assignedTo) return '??'
    const names = assignedTo.trim().split(' ')
    if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase()
    }
    const first = names[0]?.charAt(0)?.toUpperCase() || ''
    const last = names[names.length - 1]?.charAt(0)?.toUpperCase() || ''
    return `${first}${last}` || '??'
}

// Helper function to extract status text using the exact same logic as StatusBadge
// This ensures consistency between visual display and exported data
const getStatusText = (isOverDue: any): string => {
    let statusText = ''
    if (
        isOverDue?.status &&
        ['High', 'Medium', 'Low'].includes(isOverDue.status)
    ) {
        statusText = isOverDue?.days
    } else if (
        isOverDue?.status === 'Completed' &&
        isOverDue?.days === 'Save As Draft'
    ) {
        statusText = isOverDue?.days
    } else if (isOverDue?.status === 'Upcoming') {
        statusText = isOverDue?.days
    } else if (isOverDue?.status === 'Completed' && isEmpty(isOverDue?.days)) {
        statusText = isOverDue?.status
    } else if (
        isOverDue?.status === 'Completed' &&
        !isEmpty(isOverDue?.days) &&
        isOverDue?.days !== 'Save As Draft'
    ) {
        statusText = isOverDue?.days
    }
    return statusText || ''
}

// Helper function to create a compatible MaintenanceCheck object for StatusBadge
const createMaintenanceCheckForBadge = (reportItem: IReportItem) => {
    return {
        id: 0, // Not needed for display
        assignedTo: { id: 0, name: '' }, // Not needed for display
        basicComponent: { id: 0, title: null }, // Not needed for display
        inventory: { id: 0, item: null }, // Not needed for display
        status: reportItem.status || '',
        recurringID: 0, // Not needed for display
        name: reportItem.taskName,
        created: '', // Not needed for display
        severity: '', // Not needed for display
        isOverDue: reportItem.dueStatus, // This is the key property StatusBadge needs
        comments: null, // Not needed for display
        workOrderNumber: null, // Not needed for display
        startDate: '', // Not needed for display
        expires: null, // Not needed for display
        maintenanceCategoryID: 0, // Not needed for display
    }
}

// Helper function to get status color classes (similar to maintenance list)
const getStatusColorClasses = (status: string) => {
    switch (status) {
        case 'High':
            return 'text-destructive hover:text-cinnabar-800'
        case 'Upcoming':
            return 'text-warning hover:text-fire-bush-500'
        default:
            return 'hover:text-curious-blue-400'
    }
}

// Function to create columns (will be called inside component to access bp, vessel data, and vessels list)
const createMaintenanceReportColumns = (
    bp: any,
    getVesselWithIcon: any,
    vessels: any[] = [],
) =>
    createColumns<IReportItem>([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Task name" />
            ),
            cell: ({ row }: { row: any }) => {
                const item = row.original
                const overDueStatus = item.dueStatus?.status

                const taskContent = (
                    <div
                        className={cn(
                            'leading-tight truncate font-medium',
                            getStatusColorClasses(overDueStatus),
                        )}>
                        {item.taskName}
                    </div>
                )

                return (
                    <div className="flex flex-col py-2.5 gap-2">
                        {/* Show card layout on xs devices */}
                        <div className="desktop:hidden inline-flex overflow-auto items-center gap-1.5">
                            {/* Assigned To Avatar */}
                            {item.assignedTo && (
                                <Avatar className="h-8 w-8">
                                    <AvatarFallback className="text-xs">
                                        {getCrewInitials(item.assignedTo)}
                                    </AvatarFallback>
                                </Avatar>
                            )}
                            <div className="grid">
                                {taskContent}
                                {/* Inventory Item */}
                                {item.inventoryName && (
                                    <div className="flex flex-col">
                                        <span className="hover:text-curious-blue-400 text-sm">
                                            {item.inventoryName}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                        {item.dueDate && (
                            <span className="tablet-sm:hidden">
                                Due date:{' '}
                                {dayjs(item.dueDate).format('DD/MM/YY')}
                            </span>
                        )}
                        {/* Show normal table layout on larger devices */}
                        <div className="hidden desktop:block">
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    {taskContent}
                                </div>
                            </div>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.taskName || ''
                const valueB = rowB?.original?.taskName || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'inventoryName',
            header: 'Inventory',
            cellAlignment: 'left' as const,
            breakpoint: 'desktop',
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <>
                        {item.inventoryName ? (
                            <span className="hover:text-curious-blue-400">
                                {item.inventoryName}
                            </span>
                        ) : (
                            <span>-</span>
                        )}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.inventoryName || ''
                const valueB = rowB?.original?.inventoryName || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'vesselName',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Location" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'laptop',
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original

                // Find the actual vessel by name from the vessels list
                const actualVessel = vessels.find(
                    (vessel: any) => vessel.title === item.vesselName,
                )

                if (actualVessel) {
                    // Use the actual vessel data with proper ID
                    const vesselWithIcon = getVesselWithIcon(
                        actualVessel.id,
                        actualVessel,
                    )
                    return (
                        <VesselLocationDisplay
                            vessel={vesselWithIcon}
                            vesselId={actualVessel.id}
                            displayText={item.vesselName}
                        />
                    )
                } else {
                    // Fallback for vessels not found in the list
                    const vesselForIcon = {
                        id: 0,
                        title: item.vesselName,
                    }
                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon)
                    return (
                        <VesselLocationDisplay
                            vessel={vesselWithIcon}
                            displayText={item.vesselName}
                        />
                    )
                }
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.vesselName || ''
                const valueB = rowB?.original?.vesselName || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'assignedTo',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Assigned" />
            ),
            breakpoint: 'desktop',
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <>
                        {item.assignedTo && (
                            <Tooltip mobileClickable>
                                <TooltipTrigger mobileClickable>
                                    <Avatar size="sm">
                                        <AvatarFallback className="text-sm">
                                            {getCrewInitials(item.assignedTo)}
                                        </AvatarFallback>
                                    </Avatar>
                                </TooltipTrigger>
                                <TooltipContent>
                                    {item.assignedTo}
                                </TooltipContent>
                            </Tooltip>
                        )}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.assignedTo || ''
                const valueB = rowB?.original?.assignedTo || ''
                return valueA.localeCompare(valueB)
            },
        },

        {
            accessorKey: 'status',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Status" />
            ),
            breakpoint: 'laptop',
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return <>{item.status || '-'}</>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.status || ''
                const valueB = rowB?.original?.status || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'dueDate',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Due date" />
            ),
            cellAlignment: 'right' as const,
            cellClassName: 'px-2.5',
            breakpoint: 'tablet-sm',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <>
                        {item.dueDate
                            ? dayjs(item.dueDate).format('DD/MM/YY')
                            : '-'}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.dueDate || ''
                const valueB = rowB?.original?.dueDate || ''
                return dayjs(valueA).unix() - dayjs(valueB).unix()
            },
        },
        {
            accessorKey: 'dueStatus',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Due status" />
            ),
            cellClassName: 'px-2.5',
            cellAlignment: 'right' as const,
            cell: ({ row }: { row: any }) => {
                const item = row.original
                const maintenanceCheck = createMaintenanceCheckForBadge(item)

                if (!maintenanceCheck) {
                    return <div>-</div>
                }

                const overDueStatus = item.dueStatus?.status
                const overDueDays = item.dueStatus?.days

                return (
                    <>
                        {overDueStatus === 'High' ? (
                            !bp['tablet-sm'] ? (
                                <div className="alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1">
                                    {overDueDays || 'Overdue'}
                                </div>
                            ) : (
                                <StatusBadge
                                    maintenanceCheck={maintenanceCheck}
                                />
                            )
                        ) : (
                            <StatusBadge maintenanceCheck={maintenanceCheck} />
                        )}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.dueStatus?.days || ''
                const valueB = rowB?.original?.dueStatus?.days || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

// Row status evaluator for maintenance report (similar to maintenance list)
const getMaintenanceReportRowStatus = (reportItem: IReportItem): RowStatus => {
    // Skip completed, archived, or draft tasks
    if (
        reportItem.status === 'Completed' ||
        reportItem.status === 'Save_As_Draft'
    ) {
        return 'normal'
    }

    const overDueStatus = reportItem.dueStatus?.status

    // Use the pre-calculated status values from the system
    switch (overDueStatus) {
        case 'High':
            return 'overdue' // Red highlighting
        case 'Upcoming':
            return 'upcoming' // Orange highlighting
        case 'Medium':
        case 'Open':
        default:
            return 'normal' // No highlighting
    }
}

export default function MaintenanceStatusActivityReport() {
    const bp = useBreakpoints()
    const { getVesselWithIcon } = useVesselIconData()
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [category, setCategory] = useState<IDropdownItem | null>(null)
    const [status, setStatus] = useState<IDropdownItem | null>(null)
    const [crew, setCrew] = useState<IDropdownItem | null>(null)
    const [dateRange, setDateRange] = useState<DateRange>()
    const [vessels, setVessels] = useState<any[]>([])

    // Load vessels for vessel lookup by name
    const [queryVessels] = useLazyQuery(VESSEL_BRIEF_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (queryVesselResponse: any) => {
            if (queryVesselResponse.readVessels.nodes) {
                const activeVessels =
                    queryVesselResponse.readVessels.nodes.filter(
                        (vessel: any) => !vessel.archived,
                    )
                setVessels(activeVessels)
            }
        },
        onError: (error: any) => {
            console.error('queryVessels error', error)
        },
    })

    // Load vessels on component mount
    React.useEffect(() => {
        queryVessels({
            variables: {
                limit: 200,
                offset: 0,
            },
        })
    }, [queryVessels])

    // Create columns with access to bp, vessel icon data, and vessels list
    const columns = createMaintenanceReportColumns(
        bp,
        getVesselWithIcon,
        vessels,
    )

    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (data: any) => {
                // Log unique status values to understand what's available
                const statusValues = new Set()
                data?.readComponentMaintenanceChecks?.nodes?.forEach(
                    (node: any) => {
                        if (node.status) {
                            statusValues.add(node.status)
                        }
                    },
                )
            },
            onError: (error: any) => {
                console.error(
                    '❌ GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error:',
                    error,
                )
            },
        },
    )

    const handleFilterOnChange = ({ type, data }: IFilter) => {
        switch (type) {
            case 'vessels':
                // Handle both single vessel and multi-vessel selection
                if (Array.isArray(data)) {
                    setSelectedVessels(data)
                } else if (data) {
                    // Single vessel selection - convert to array for consistency
                    setSelectedVessels([data])
                } else {
                    // Clear selection
                    setSelectedVessels([])
                }
                break
            case 'category':
                setCategory(data)
                break

            case 'status':
                setStatus(data)
                break

            case 'dateRange':
                setDateRange(data)
                break

            case 'member':
                setCrew(data)
                break

            default:
                break
        }
    }

    const generateReport = () => {
        const filter: any = {}

        if (
            dateRange &&
            dateRange.startDate !== null &&
            dateRange.endDate !== null
        ) {
            // Format dates as YYYY-MM-DD strings for GraphQL
            const startDateFormatted = dayjs(dateRange.startDate).format(
                'YYYY-MM-DD',
            )
            const endDateFormatted = dayjs(dateRange.endDate).format(
                'YYYY-MM-DD',
            )

            filter['expires'] = {
                gte: startDateFormatted,
                lte: endDateFormatted,
            }
        }

        if (selectedVessels && selectedVessels.length > 0) {
            filter['basicComponentID'] = {
                in: selectedVessels.map((item) => +item.value), // Convert to numbers
            }
        }

        if (category !== null) {
            filter['maintenanceCategoryID'] = {
                eq: +category.value, // Convert to number like other filters
            }
        }

        if (status !== null) {
            filter['status'] = {
                eq: status.value, // Status is a string, no need to convert
            }
        }

        if (crew !== null) {
            filter['assignedToID'] = {
                eq: +crew.value, // Convert to number
            }
        }

        getReportData({
            variables: {
                filter,
            },
        })
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const body = reportData.map((item: any) => {
            return [
                item.taskName,
                item.inventoryName ?? '',
                item.vesselName ?? '',
                item.assignedTo ?? '',
                item.status ?? '',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : '',
                getStatusText(item.dueStatus),
            ]
        })

        const headers: any = [
            [
                'Task Name',
                'Inventory',
                'Location',
                'Assigned To',
                'Status',
                'Due Date',
                'Due Status',
            ],
        ]

        exportPdfTable({
            body,
            headers,
        })
    }

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries: string[][] = [
            [
                'task name',
                'inventory',
                'location',
                'assigned to',
                'status',
                'due date',
                'due status',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                item.taskName,
                item.inventoryName ?? 'N/A',
                item.vesselName ?? 'N/A',
                item.assignedTo ?? 'N/A',
                item.status ?? 'N/A',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : 'N/A',
                getStatusText(item.dueStatus),
            ])
        })

        exportCsv(csvEntries)
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readComponentMaintenanceChecks.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach((fetchedItem: any) => {
            const reportItem: IReportItem = {
                taskName: fetchedItem.name,
                vesselName: fetchedItem.basicComponent.title,
                assignedTo:
                    fetchedItem.assignedTo.id == 0
                        ? undefined
                        : `${fetchedItem.assignedTo.firstName} ${fetchedItem.assignedTo.surname}`,
                inventoryName: fetchedItem.inventory.title,
                dueDate: fetchedItem.expires
                    ? new Date(fetchedItem.expires)
                    : undefined,
                status: fetchedItem.status,
                dueStatus: isOverDueTask(fetchedItem),
            }
            reportItems.push(reportItem)
        })

        return reportItems
    }, [called, loading, data])

    return (
        <>
            <ListHeader
                title="Maintenance status and activity report"
                actions={
                    <MaintenanceReportFilterActions
                        onDownloadCsv={downloadCsv}
                        onDownloadPdf={downloadPdf}
                    />
                }
            />
            <div className="mt-16">
                <DataTable
                    columns={columns}
                    data={reportData}
                    isLoading={called && loading}
                    rowStatus={getMaintenanceReportRowStatus}
                    onChange={handleFilterOnChange}
                    onFilterClick={generateReport}
                    showToolbar={true}
                />
            </div>
        </>
    )
}

export const dueStatusLabel = (dueStatus: any): string => {
    return `${
        dueStatus?.status &&
        ['High', 'Medium', 'Low'].includes(dueStatus.status)
            ? dueStatus?.days
            : ''
    }${
        dueStatus?.status === 'Completed' && dueStatus?.days === 'Save As Draft'
            ? dueStatus?.days
            : ''
    }${dueStatus?.status === 'Upcoming' ? dueStatus?.days : ''}${
        dueStatus?.status === 'Completed' && isEmpty(dueStatus?.days)
            ? dueStatus?.status
            : ''
    }${
        dueStatus?.status === 'Completed' &&
        !isEmpty(dueStatus?.days) &&
        dueStatus?.days !== 'Save As Draft'
            ? dueStatus?.days
            : ''
    }`
}
