"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/ui/maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ var _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions for generating initials (similar to maintenance list)\nconst getCrewInitials = (assignedTo)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!assignedTo) return \"??\";\n    const names = assignedTo.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Helper function to extract status text using the exact same logic as StatusBadge\n// This ensures consistency between visual display and exported data\nconst getStatusText = (isOverDue)=>{\n    let statusText = \"\";\n    if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(isOverDue.status)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) === \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Upcoming\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) !== \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    }\n    return statusText || \"\";\n};\n// Helper function to create a compatible MaintenanceCheck object for StatusBadge\nconst createMaintenanceCheckForBadge = (reportItem)=>{\n    return {\n        id: 0,\n        assignedTo: {\n            id: 0,\n            name: \"\"\n        },\n        basicComponent: {\n            id: 0,\n            title: null\n        },\n        inventory: {\n            id: 0,\n            item: null\n        },\n        status: reportItem.status || \"\",\n        recurringID: 0,\n        name: reportItem.taskName,\n        created: \"\",\n        severity: \"\",\n        isOverDue: reportItem.dueStatus,\n        comments: null,\n        workOrderNumber: null,\n        startDate: \"\",\n        expires: null,\n        maintenanceCategoryID: 0\n    };\n};\n// Helper function to get status color classes (similar to maintenance list)\nconst getStatusColorClasses = (status)=>{\n    switch(status){\n        case \"High\":\n            return \"text-destructive hover:text-cinnabar-800\";\n        case \"Upcoming\":\n            return \"text-warning hover:text-fire-bush-500\";\n        default:\n            return \"hover:text-curious-blue-400\";\n    }\n};\n// Function to create columns (will be called inside component to access bp, vessel data, and vessels list)\nconst createMaintenanceReportColumns = function(bp, getVesselWithIcon) {\n    let vessels = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Task name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 17\n                }, _this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus;\n                const item = row.original;\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const taskContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"leading-tight truncate font-medium\", getStatusColorClasses(overDueStatus)),\n                    children: item.taskName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 21\n                }, _this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col py-2.5 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"desktop:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 37\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 33\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        taskContent,\n                                        item.inventoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-curious-blue-400 text-sm\",\n                                                children: item.inventoryName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 41\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 37\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 29\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 25\n                        }, _this),\n                        item.dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"tablet-sm:hidden\",\n                            children: [\n                                \"Due date: \",\n                                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 29\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden desktop:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: taskContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 33\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 29\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 25\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.taskName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.taskName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventoryName\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.inventoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hover:text-curious-blue-400\",\n                        children: item.inventoryName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 29\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.inventoryName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.inventoryName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Location\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Find the actual vessel by name from the vessels list\n                const actualVessel = vessels.find((vessel)=>vessel.title === item.vesselName);\n                if (actualVessel) {\n                    // Use the actual vessel data with proper ID\n                    const vesselWithIcon = getVesselWithIcon(actualVessel.id, actualVessel);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        vesselId: actualVessel.id,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 25\n                    }, _this);\n                } else {\n                    // Fallback for vessels not found in the list\n                    const vesselForIcon = {\n                        id: 0,\n                        title: item.vesselName\n                    };\n                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 25\n                    }, _this);\n                }\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"assignedTo\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 17\n                }, _this);\n            },\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        mobileClickable: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                mobileClickable: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 41\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 37\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 33\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                children: item.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 33\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.assignedTo) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.assignedTo) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 17\n                }, _this);\n            },\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.status || \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.status) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.status) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"dueDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"dueStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellClassName: \"px-2.5\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus, _item_dueStatus1;\n                const item = row.original;\n                const maintenanceCheck = createMaintenanceCheckForBadge(item);\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 28\n                    }, _this);\n                }\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const overDueDays = (_item_dueStatus1 = item.dueStatus) === null || _item_dueStatus1 === void 0 ? void 0 : _item_dueStatus1.days;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n                        children: overDueDays || \"Overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 33\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 33\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_dueStatus, _rowA_original, _rowB_original_dueStatus, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_dueStatus = _rowA_original.dueStatus) === null || _rowA_original_dueStatus === void 0 ? void 0 : _rowA_original_dueStatus.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_dueStatus = _rowB_original.dueStatus) === null || _rowB_original_dueStatus === void 0 ? void 0 : _rowB_original_dueStatus.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n};\n// Row status evaluator for maintenance report (similar to maintenance list)\nconst getMaintenanceReportRowStatus = (reportItem)=>{\n    var _reportItem_dueStatus;\n    // Skip completed, archived, or draft tasks\n    if (reportItem.status === \"Completed\" || reportItem.status === \"Save_As_Draft\") {\n        return \"normal\";\n    }\n    const overDueStatus = (_reportItem_dueStatus = reportItem.dueStatus) === null || _reportItem_dueStatus === void 0 ? void 0 : _reportItem_dueStatus.status;\n    // Use the pre-calculated status values from the system\n    switch(overDueStatus){\n        case \"High\":\n            return \"overdue\" // Red highlighting\n            ;\n        case \"Upcoming\":\n            return \"upcoming\" // Orange highlighting\n            ;\n        case \"Medium\":\n        case \"Open\":\n        default:\n            return \"normal\" // No highlighting\n            ;\n    }\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load vessels for vessel lookup by name\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_18__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                const activeVessels = queryVesselResponse.readVessels.nodes.filter((vessel)=>!vessel.archived);\n                setVessels(activeVessels);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // Load vessels on component mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    }, [\n        queryVessels\n    ]);\n    // Create columns with access to bp, vessel icon data, and vessels list\n    const columns = createMaintenanceReportColumns(bp, getVesselWithIcon, vessels);\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readComponentMaintenanceChecks_nodes, _data_readComponentMaintenanceChecks;\n            // Log unique status values to understand what's available\n            const statusValues = new Set();\n            data === null || data === void 0 ? void 0 : (_data_readComponentMaintenanceChecks = data.readComponentMaintenanceChecks) === null || _data_readComponentMaintenanceChecks === void 0 ? void 0 : (_data_readComponentMaintenanceChecks_nodes = _data_readComponentMaintenanceChecks.nodes) === null || _data_readComponentMaintenanceChecks_nodes === void 0 ? void 0 : _data_readComponentMaintenanceChecks_nodes.forEach((node)=>{\n                if (node.status) {\n                    statusValues.add(node.status);\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"❌ GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error:\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>+item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: +category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: +crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                getStatusText(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                getStatusText(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__.MaintenanceReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 676,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    rowStatus: getMaintenanceReportRowStatus,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 686,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 685,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"bXSSC/NOcU8MfvYORn+MZ1Yz9d0=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});