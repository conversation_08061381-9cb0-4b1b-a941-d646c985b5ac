"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _export_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./export-button */ \"(app-pages-browser)/./src/app/ui/reporting/export-button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction NewCrewSeatimeReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readCrewMembers_LogBookEntrySections_nodes, _data_readCrewMembers_LogBookEntrySections;\n            console.log(\"✅ GraphQL query completed successfully\");\n            console.log(\"\\uD83D\\uDCE6 Raw data received:\", data);\n            console.log(\"\\uD83D\\uDCCA Nodes count:\", (data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections_nodes = _data_readCrewMembers_LogBookEntrySections.nodes) === null || _data_readCrewMembers_LogBookEntrySections_nodes === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections_nodes.length) || 0);\n        },\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD0D generateReport called\");\n        console.log(\"\\uD83D\\uDCCA Current state:\", {\n            selectedCrews,\n            selectedDuties,\n            selectedVessels,\n            dateRange\n        });\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n            console.log(\"\\uD83D\\uDC65 Added crew filter:\", filter[\"crewMemberID\"]);\n        }\n        if (selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n            console.log(\"\\uD83D\\uDCBC Added duty filter:\", filter[\"dutyPerformedID\"]);\n        }\n        if (selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n            console.log(\"\\uD83D\\uDEA2 Added vessel filter:\", logBookFilter.vehicleID);\n        }\n        if (dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n            console.log(\"\\uD83D\\uDCC5 Added date filter:\", logBookFilter.startDate);\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n            console.log(\"\\uD83D\\uDCD6 Added logBookEntry filter:\", filter[\"logBookEntry\"]);\n        }\n        console.log(\"\\uD83C\\uDFAF Final filter object:\", filter);\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        if (reportMode === \"detailed\") {\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 340,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onChange: handleFilterOnChange,\n                            onClick: generateReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_export_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            onDownloadPdf: downloadPdf,\n                            onDownloadCsv: downloadCsv\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Crew member name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Vessel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Duty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Time spent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                        isLoading: called && loading,\n                                        reportData: reportData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 352,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewCrewSeatimeReport, \"VJQnQGZC8TuqXzt4SgMe2Ge87SA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c = NewCrewSeatimeReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 396,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 395,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"No Data Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 406,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 405,\n            columnNumber: 13\n        }, this);\n    }\n    return reportData.map((element, index)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            className: \"group border-b  hover: \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block ml-3\",\n                        children: element.crewName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.primaryDuty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.loginTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.logoutTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: [\n                            element.loggedDuration.hours != 0 ? \"\".concat(element.loggedDuration.hours, \"h, \") : \"\",\n                            element.loggedDuration.minutes,\n                            \"m\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, \"\".concat(element.crewID, \"-\").concat(index), true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 415,\n            columnNumber: 13\n        }, this);\n    });\n}\n_c1 = TableContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewCrewSeatimeReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});