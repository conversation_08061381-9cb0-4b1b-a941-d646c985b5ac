"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/ui/maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ var _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions for generating initials (similar to maintenance list)\nconst getCrewInitials = (assignedTo)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!assignedTo) return \"??\";\n    const names = assignedTo.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Helper function to extract status text using the exact same logic as StatusBadge\n// This ensures consistency between visual display and exported data\nconst getStatusText = (isOverDue)=>{\n    let statusText = \"\";\n    if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(isOverDue.status)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) === \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Upcoming\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) !== \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    }\n    return statusText || \"\";\n};\n// Helper function to create a compatible MaintenanceCheck object for StatusBadge\nconst createMaintenanceCheckForBadge = (reportItem)=>{\n    return {\n        id: 0,\n        assignedTo: {\n            id: 0,\n            name: \"\"\n        },\n        basicComponent: {\n            id: 0,\n            title: null\n        },\n        inventory: {\n            id: 0,\n            item: null\n        },\n        status: reportItem.status || \"\",\n        recurringID: 0,\n        name: reportItem.taskName,\n        created: \"\",\n        severity: \"\",\n        isOverDue: reportItem.dueStatus,\n        comments: null,\n        workOrderNumber: null,\n        startDate: \"\",\n        expires: null,\n        maintenanceCategoryID: 0\n    };\n};\n// Helper function to get status color classes (similar to maintenance list)\nconst getStatusColorClasses = (status)=>{\n    switch(status){\n        case \"High\":\n            return \"text-destructive hover:text-cinnabar-800\";\n        case \"Upcoming\":\n            return \"text-warning hover:text-fire-bush-500\";\n        default:\n            return \"hover:text-curious-blue-400\";\n    }\n};\n// Function to create columns (will be called inside component to access bp, vessel data, and vessels list)\nconst createMaintenanceReportColumns = function(bp, getVesselWithIcon) {\n    let vessels = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Task Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 17\n                }, _this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus;\n                const item = row.original;\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const taskContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"leading-tight truncate font-medium\", getStatusColorClasses(overDueStatus)),\n                    children: item.taskName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 21\n                }, _this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"tablet-sm:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 37\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 33\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        taskContent,\n                                        item.inventoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-curious-blue-400 text-sm\",\n                                                children: item.inventoryName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 41\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 37\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 29\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 25\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden tablet-sm:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: taskContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 33\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: item.vesselName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-outer-space-400\",\n                                                    children: [\n                                                        \"Location:\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 45\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hover:text-curious-blue-400\",\n                                                    children: item.vesselName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 45\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 41\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 33\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 29\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 25\n                        }, _this)\n                    ]\n                }, void 0, true);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.taskName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.taskName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventoryName\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.inventoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hover:text-curious-blue-400\",\n                        children: item.inventoryName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 29\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.inventoryName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.inventoryName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Location\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Find the actual vessel by name from the vessels list\n                const actualVessel = vessels.find((vessel)=>vessel.title === item.vesselName);\n                if (actualVessel) {\n                    // Use the actual vessel data with proper ID\n                    const vesselWithIcon = getVesselWithIcon(actualVessel.id, actualVessel);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        vesselId: actualVessel.id,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 25\n                    }, _this);\n                } else {\n                    // Fallback for vessels not found in the list\n                    const vesselForIcon = {\n                        id: 0,\n                        title: item.vesselName\n                    };\n                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 25\n                    }, _this);\n                }\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"assignedTo\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 17\n                }, _this);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        mobileClickable: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                mobileClickable: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 41\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 37\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 33\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                children: item.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 33\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.assignedTo) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.assignedTo) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 17\n                }, _this);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.status || \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.status) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.status) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"dueDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"right\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"dueStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellClassName: \"px-2\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus, _item_dueStatus1;\n                const item = row.original;\n                const maintenanceCheck = createMaintenanceCheckForBadge(item);\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 28\n                    }, _this);\n                }\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const overDueDays = (_item_dueStatus1 = item.dueStatus) === null || _item_dueStatus1 === void 0 ? void 0 : _item_dueStatus1.days;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n                        children: overDueDays || \"Overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 33\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 33\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_dueStatus, _rowA_original, _rowB_original_dueStatus, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_dueStatus = _rowA_original.dueStatus) === null || _rowA_original_dueStatus === void 0 ? void 0 : _rowA_original_dueStatus.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_dueStatus = _rowB_original.dueStatus) === null || _rowB_original_dueStatus === void 0 ? void 0 : _rowB_original_dueStatus.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n};\n// Row status evaluator for maintenance report (similar to maintenance list)\nconst getMaintenanceReportRowStatus = (reportItem)=>{\n    var _reportItem_dueStatus;\n    // Skip completed, archived, or draft tasks\n    if (reportItem.status === \"Completed\" || reportItem.status === \"Save_As_Draft\") {\n        return \"normal\";\n    }\n    const overDueStatus = (_reportItem_dueStatus = reportItem.dueStatus) === null || _reportItem_dueStatus === void 0 ? void 0 : _reportItem_dueStatus.status;\n    // Use the pre-calculated status values from the system\n    switch(overDueStatus){\n        case \"High\":\n            return \"overdue\" // Red highlighting\n            ;\n        case \"Upcoming\":\n            return \"upcoming\" // Orange highlighting\n            ;\n        case \"Medium\":\n        case \"Open\":\n        default:\n            return \"normal\" // No highlighting\n            ;\n    }\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load vessels for vessel lookup by name\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_18__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                const activeVessels = queryVesselResponse.readVessels.nodes.filter((vessel)=>!vessel.archived);\n                setVessels(activeVessels);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // Load vessels on component mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    }, [\n        queryVessels\n    ]);\n    // Create columns with access to bp, vessel icon data, and vessels list\n    const columns = createMaintenanceReportColumns(bp, getVesselWithIcon, vessels);\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readComponentMaintenanceChecks_nodes, _data_readComponentMaintenanceChecks;\n            // Log unique status values to understand what's available\n            const statusValues = new Set();\n            data === null || data === void 0 ? void 0 : (_data_readComponentMaintenanceChecks = data.readComponentMaintenanceChecks) === null || _data_readComponentMaintenanceChecks === void 0 ? void 0 : (_data_readComponentMaintenanceChecks_nodes = _data_readComponentMaintenanceChecks.nodes) === null || _data_readComponentMaintenanceChecks_nodes === void 0 ? void 0 : _data_readComponentMaintenanceChecks_nodes.forEach((node)=>{\n                if (node.status) {\n                    statusValues.add(node.status);\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"❌ GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error:\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>+item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: +category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: +crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                getStatusText(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                getStatusText(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__.MaintenanceReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 681,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    rowStatus: getMaintenanceReportRowStatus,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 691,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 690,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"bXSSC/NOcU8MfvYORn+MZ1Yz9d0=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});