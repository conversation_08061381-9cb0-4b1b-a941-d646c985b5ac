"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function for generating crew member initials\nconst getCrewInitials = (crewName)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!crewName) return \"??\";\n    const names = crewName.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Function to create columns for the crew seatime report\nconst createCrewSeatimeColumns = function(bp, getVesselWithIcon) {\n    let vessels = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Crew member name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 17\n                }, _this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                const crewContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"leading-tight truncate font-medium hover:text-curious-blue-400\",\n                    children: item.crewName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 21\n                }, _this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col py-2.5 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"desktop:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.crewName)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 33\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 29\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        crewContent,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.vesselName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 37\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.primaryDuty\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 37\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 33\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 29\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 25\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden desktop:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                        size: \"sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: getCrewInitials(item.crewName)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 37\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 33\n                                    }, _this),\n                                    crewContent\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 29\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 25\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.crewName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.crewName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Find the actual vessel by name from the vessels list\n                const actualVessel = vessels.find((vessel)=>vessel.title === item.vesselName);\n                if (actualVessel) {\n                    // Use the actual vessel data with proper ID\n                    const vesselWithIcon = getVesselWithIcon(actualVessel.id, actualVessel);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        vesselId: actualVessel.id,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 25\n                    }, _this);\n                } else {\n                    // Fallback for vessels not found in the list\n                    const vesselForIcon = {\n                        id: 0,\n                        title: item.vesselName\n                    };\n                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 25\n                    }, _this);\n                }\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Duty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hover:text-curious-blue-400\",\n                    children: item.primaryDuty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.primaryDuty) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.primaryDuty) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"loginTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Signed in\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/M/YY HH:mm\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.loginTime) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.loginTime) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"logoutTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Signed out\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/M/YY HH:mm\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.logoutTime) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.logoutTime) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"loggedDuration\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Time spent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        item.loggedDuration.hours != 0 ? \"\".concat(item.loggedDuration.hours, \"h, \") : \"\",\n                        item.loggedDuration.minutes,\n                        \"m\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.totalLoggedMinutes) || 0;\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.totalLoggedMinutes) || 0;\n                return valueA - valueB;\n            }\n        }\n    ]);\n};\nconst CrewSeatimeReportActions = (param)=>{\n    let { onDownloadCsv, onDownloadPdf } = param;\n    _s();\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_15__.useSidebar)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_16__.SealogsCogIcon, {\n                    size: 36\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 299,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuContent, {\n                side: isMobile ? \"bottom\" : \"right\",\n                align: isMobile ? \"end\" : \"start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-input flex flex-col items-center justify-center py-[9px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                            variant: \"backButton\",\n                            onClick: ()=>router.push(\"/reporting\"),\n                            children: \"Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 21\n                        }, undefined),\n                        onDownloadPdf && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadPdf,\n                            children: \"Download PDF\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 25\n                        }, undefined),\n                        onDownloadCsv && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_14__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadCsv,\n                            children: \"Download CSV\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 302,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n        lineNumber: 298,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewSeatimeReportActions, \"2jIoXD9G8OZZK/Hd0W/SlED0TzQ=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_15__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = CrewSeatimeReportActions;\nfunction NewCrewSeatimeReport() {\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__.useVesselIconData)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: null,\n        endDate: null\n    });\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load vessels for vessel lookup by name\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                const activeVessels = queryVesselResponse.readVessels.nodes.filter((vessel)=>!vessel.archived);\n                setVessels(activeVessels);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // Load vessels on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    }, [\n        queryVessels\n    ]);\n    // Auto-generate report on page load if no filters are actively selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hasActiveFilters = selectedCrews.length > 0 || selectedDuties.length > 0 || selectedVessels.length > 0 || dateRange.startDate !== null && dateRange.endDate !== null;\n        if (!hasActiveFilters) {\n            generateReport();\n        }\n    }, []) // Only run on mount\n    ;\n    // Create columns with access to bp, vessel icon data, and vessels list\n    const columns = createCrewSeatimeColumns(bp, getVesselWithIcon, vessels);\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{},\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews && selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n        }\n        if (selectedDuties && selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n        }\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        if (reportMode === \"detailed\") {\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CrewSeatimeReportActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 651,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 661,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 660,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(NewCrewSeatimeReport, \"9cE7SRlKenMH8i/rWNebmfHLqZI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c1 = NewCrewSeatimeReport;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewSeatimeReportActions\");\n$RefreshReg$(_c1, \"NewCrewSeatimeReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});