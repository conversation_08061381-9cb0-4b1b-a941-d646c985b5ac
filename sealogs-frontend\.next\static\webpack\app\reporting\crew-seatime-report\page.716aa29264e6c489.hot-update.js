"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function for generating crew member initials\nconst getCrewInitials = (crewName)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!crewName) return \"??\";\n    const names = crewName.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Function to create columns for the crew seatime report\nconst createCrewSeatimeColumns = function(bp, getVesselWithIcon) {\n    let vessels = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"crewName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Crew member name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 17\n                }, _this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                const crewContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"leading-tight truncate font-medium hover:text-curious-blue-400\",\n                    children: item.crewName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 21\n                }, _this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col py-2.5 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"desktop:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.crewName)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 33\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 29\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        crewContent,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.vesselName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 37\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.primaryDuty\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 37\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 33\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 29\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 25\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden desktop:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                        size: \"sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: getCrewInitials(item.crewName)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 37\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 33\n                                    }, _this),\n                                    crewContent\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 29\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 25\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.crewName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.crewName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Find the actual vessel by name from the vessels list\n                const actualVessel = vessels.find((vessel)=>vessel.title === item.vesselName);\n                if (actualVessel) {\n                    // Use the actual vessel data with proper ID\n                    const vesselWithIcon = getVesselWithIcon(actualVessel.id, actualVessel);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        vesselId: actualVessel.id,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 25\n                    }, _this);\n                } else {\n                    // Fallback for vessels not found in the list\n                    const vesselForIcon = {\n                        id: 0,\n                        title: item.vesselName\n                    };\n                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_11__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 25\n                    }, _this);\n                }\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Duty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hover:text-curious-blue-400\",\n                    children: item.primaryDuty\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.primaryDuty) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.primaryDuty) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"loginTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Signed in\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/M/YY HH:mm\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.loginTime) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.loginTime) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"logoutTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Signed out\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/M/YY HH:mm\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.logoutTime) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.logoutTime) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_3___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"loggedDuration\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Time spent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        item.loggedDuration.hours != 0 ? \"\".concat(item.loggedDuration.hours, \"h, \") : \"\",\n                        item.loggedDuration.minutes,\n                        \"m\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.totalLoggedMinutes) || 0;\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.totalLoggedMinutes) || 0;\n                return valueA - valueB;\n            }\n        }\n    ]);\n};\nfunction NewCrewSeatimeReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__.useVesselIconData)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: null,\n        endDate: null\n    });\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load vessels for vessel lookup by name\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                const activeVessels = queryVesselResponse.readVessels.nodes.filter((vessel)=>!vessel.archived);\n                setVessels(activeVessels);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // Load vessels on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    }, [\n        queryVessels\n    ]);\n    // Auto-generate report on page load if no filters are actively selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hasActiveFilters = selectedCrews.length > 0 || selectedDuties.length > 0 || selectedVessels.length > 0 || dateRange.startDate !== null && dateRange.endDate !== null;\n        if (!hasActiveFilters) {\n            generateReport();\n        }\n    }, []) // Only run on mount\n    ;\n    // Create columns with access to bp, vessel icon data, and vessels list\n    const columns = createCrewSeatimeColumns(bp, getVesselWithIcon, vessels);\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{},\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews && selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n        }\n        if (selectedDuties && selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n        }\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        if (reportMode === \"detailed\") {\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 596,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Filter, {\n                            onChange: handleFilterOnChange,\n                            onClick: generateReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExportButton, {\n                            onDownloadPdf: downloadPdf,\n                            onDownloadCsv: downloadCsv\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Crew member name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Vessel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Duty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Signed in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Signed out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: \"Time spent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                        isLoading: called && loading,\n                                        reportData: reportData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 608,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewCrewSeatimeReport, \"9cE7SRlKenMH8i/rWNebmfHLqZI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_9__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_10__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\n_c = NewCrewSeatimeReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 652,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 651,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"No Data Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 662,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 661,\n            columnNumber: 13\n        }, this);\n    }\n    return reportData.map((element, index)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            className: \"group border-b  hover: \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block ml-3\",\n                        children: element.crewName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 675,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 674,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 677,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.primaryDuty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(element.loginTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 684,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 683,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(element.logoutTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 689,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 688,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: [\n                            element.loggedDuration.hours != 0 ? \"\".concat(element.loggedDuration.hours, \"h, \") : \"\",\n                            element.loggedDuration.minutes,\n                            \"m\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, \"\".concat(element.crewID, \"-\").concat(index), true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 671,\n            columnNumber: 13\n        }, this);\n    });\n}\n_c1 = TableContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewCrewSeatimeReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});