"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _export_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./export-button */ \"(app-pages-browser)/./src/app/ui/reporting/export-button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction NewCrewSeatimeReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDE80 NewCrewSeatimeReport component mounted\");\n        console.log(\"\\uD83D\\uDCCA Initial state:\", {\n            selectedCrews,\n            selectedDuties,\n            selectedVessels,\n            reportMode,\n            dateRange\n        });\n        // Temporary alert to confirm component is mounting\n        alert(\"NewCrewSeatimeReport component mounted!\");\n    }, []);\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD27 Filter changed:\", type, data);\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readCrewMembers_LogBookEntrySections_nodes, _data_readCrewMembers_LogBookEntrySections;\n            console.log(\"✅ GraphQL query completed successfully\");\n            console.log(\"\\uD83D\\uDCE6 Raw data received:\", data);\n            console.log(\"\\uD83D\\uDCCA Nodes count:\", (data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections_nodes = _data_readCrewMembers_LogBookEntrySections.nodes) === null || _data_readCrewMembers_LogBookEntrySections_nodes === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections_nodes.length) || 0);\n        },\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD0D generateReport called\");\n        console.log(\"\\uD83D\\uDCCA Current state:\", {\n            selectedCrews,\n            selectedDuties,\n            selectedVessels,\n            dateRange\n        });\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews && selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n            console.log(\"\\uD83D\\uDC65 Added crew filter:\", filter[\"crewMemberID\"]);\n        }\n        if (selectedDuties && selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n            console.log(\"\\uD83D\\uDCBC Added duty filter:\", filter[\"dutyPerformedID\"]);\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n            console.log(\"\\uD83D\\uDEA2 Added vessel filter:\", logBookFilter.vehicleID);\n        }\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n            console.log(\"\\uD83D\\uDCC5 Added date filter:\", logBookFilter.startDate);\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n            console.log(\"\\uD83D\\uDCD6 Added logBookEntry filter:\", filter[\"logBookEntry\"]);\n        }\n        console.log(\"\\uD83C\\uDFAF Final filter object:\", filter);\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        console.log(\"\\uD83D\\uDD04 Processing reportData useMemo\");\n        console.log(\"\\uD83D\\uDCCA called:\", called, \"loading:\", loading);\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        console.log(\"\\uD83D\\uDCE6 Raw reportData length:\", reportData.length);\n        console.log(\"\\uD83D\\uDCE6 Raw reportData sample:\", reportData.slice(0, 2));\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        console.log(\"\\uD83D\\uDD0D Filtered data (punchOut !== null) length:\", filteredData.length);\n        console.log(\"\\uD83D\\uDD0D Filtered data sample:\", filteredData.slice(0, 2));\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        console.log(\"\\uD83D\\uDCCB Report items created:\", reportItems.length);\n        console.log(\"\\uD83D\\uDCCB Report items sample:\", reportItems.slice(0, 2));\n        console.log(\"\\uD83C\\uDFAF Report mode:\", reportMode);\n        if (reportMode === \"detailed\") {\n            console.log(\"✅ Returning detailed report with\", reportItems.length, \"items\");\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        console.log(\"✅ Returning summarized report with\", summarizedReportItems.length, \"items\");\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 384,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onChange: handleFilterOnChange,\n                            onClick: generateReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_export_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            onDownloadPdf: downloadPdf,\n                            onDownloadCsv: downloadCsv\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Crew member name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Vessel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Duty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Time spent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                        isLoading: called && loading,\n                                        reportData: reportData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 396,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewCrewSeatimeReport, \"yNZW2+JNzpS40rCLUUbQ1DyQT3A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c = NewCrewSeatimeReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 440,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 439,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"No Data Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 450,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 449,\n            columnNumber: 13\n        }, this);\n    }\n    return reportData.map((element, index)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            className: \"group border-b  hover: \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block ml-3\",\n                        children: element.crewName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.primaryDuty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.loginTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.logoutTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 476,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: [\n                            element.loggedDuration.hours != 0 ? \"\".concat(element.loggedDuration.hours, \"h, \") : \"\",\n                            element.loggedDuration.minutes,\n                            \"m\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, \"\".concat(element.crewID, \"-\").concat(index), true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 459,\n            columnNumber: 13\n        }, this);\n    });\n}\n_c1 = TableContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewCrewSeatimeReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});