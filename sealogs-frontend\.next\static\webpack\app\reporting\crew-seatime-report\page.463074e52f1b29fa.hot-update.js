"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/crew-seatime-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/reporting/new-crew-seatime-report.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewCrewSeatimeReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _export_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./export-button */ \"(app-pages-browser)/./src/app/ui/reporting/export-button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction NewCrewSeatimeReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [selectedCrews, setSelectedCrews] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedDuties, setSelectedDuties] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [reportMode, setReportMode] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"detailed\");\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDE80 NewCrewSeatimeReport component mounted\");\n        console.log(\"\\uD83D\\uDCCA Initial state:\", {\n            selectedCrews,\n            selectedDuties,\n            selectedVessels,\n            reportMode,\n            dateRange\n        });\n    }, []);\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD27 Filter changed:\", type, data);\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"members\":\n                setSelectedCrews(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"crewDuty\":\n                setSelectedDuties(data);\n                break;\n            case \"reportMode\":\n                setReportMode(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readCrewMembers_LogBookEntrySections_nodes, _data_readCrewMembers_LogBookEntrySections;\n            console.log(\"✅ GraphQL query completed successfully\");\n            console.log(\"\\uD83D\\uDCE6 Raw data received:\", data);\n            console.log(\"\\uD83D\\uDCCA Nodes count:\", (data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections_nodes = _data_readCrewMembers_LogBookEntrySections.nodes) === null || _data_readCrewMembers_LogBookEntrySections_nodes === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections_nodes.length) || 0);\n        },\n        onError: (error)=>{\n            console.error(\"❌ queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD0D generateReport called\");\n        console.log(\"\\uD83D\\uDCCA Current state:\", {\n            selectedCrews,\n            selectedDuties,\n            selectedVessels,\n            dateRange\n        });\n        const filter = {};\n        const logBookFilter = {\n            vehicleID: null,\n            startDate: null\n        };\n        if (selectedCrews.length > 0) {\n            filter[\"crewMemberID\"] = {\n                in: selectedCrews.map((crew)=>crew.value)\n            };\n            console.log(\"\\uD83D\\uDC65 Added crew filter:\", filter[\"crewMemberID\"]);\n        }\n        if (selectedDuties.length > 0) {\n            filter[\"dutyPerformedID\"] = {\n                in: selectedDuties.map((duty)=>duty.value)\n            };\n            console.log(\"\\uD83D\\uDCBC Added duty filter:\", filter[\"dutyPerformedID\"]);\n        }\n        if (selectedVessels.length > 0) {\n            logBookFilter.vehicleID = {\n                in: selectedVessels.map((vessel)=>vessel.value)\n            };\n            console.log(\"\\uD83D\\uDEA2 Added vessel filter:\", logBookFilter.vehicleID);\n        }\n        if (dateRange.startDate !== null && dateRange.endDate !== null) {\n            logBookFilter.startDate = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n            console.log(\"\\uD83D\\uDCC5 Added date filter:\", logBookFilter.startDate);\n        }\n        if (logBookFilter.vehicleID !== null || logBookFilter.startDate !== null) {\n            if (logBookFilter.vehicleID === null) {\n                delete logBookFilter.vehicleID;\n            }\n            if (logBookFilter.startDate === null) {\n                delete logBookFilter.startDate;\n            }\n            filter[\"logBookEntry\"] = logBookFilter;\n            console.log(\"\\uD83D\\uDCD6 Added logBookEntry filter:\", filter[\"logBookEntry\"]);\n        }\n        console.log(\"\\uD83C\\uDFAF Final filter object:\", filter);\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    }, [\n        selectedCrews,\n        selectedDuties,\n        selectedVessels,\n        dateRange,\n        getReportData\n    ]);\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        var _data_readCrewMembers_LogBookEntrySections;\n        console.log(\"\\uD83D\\uDD04 Processing reportData useMemo\");\n        console.log(\"\\uD83D\\uDCCA called:\", called, \"loading:\", loading);\n        var _data_readCrewMembers_LogBookEntrySections_nodes;\n        const reportData = (_data_readCrewMembers_LogBookEntrySections_nodes = data === null || data === void 0 ? void 0 : (_data_readCrewMembers_LogBookEntrySections = data.readCrewMembers_LogBookEntrySections) === null || _data_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _data_readCrewMembers_LogBookEntrySections.nodes) !== null && _data_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _data_readCrewMembers_LogBookEntrySections_nodes : [];\n        console.log(\"\\uD83D\\uDCE6 Raw reportData length:\", reportData.length);\n        console.log(\"\\uD83D\\uDCE6 Raw reportData sample:\", reportData.slice(0, 2));\n        const filteredData = reportData.filter((item)=>item.punchOut !== null);\n        console.log(\"\\uD83D\\uDD0D Filtered data (punchOut !== null) length:\", filteredData.length);\n        console.log(\"\\uD83D\\uDD0D Filtered data sample:\", filteredData.slice(0, 2));\n        const reportItems = filteredData.map((item)=>{\n            const loggedInTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchIn);\n            const loggedOutTime = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.punchOut);\n            const loggedDurationMinutes = loggedOutTime.diff(loggedInTime, \"minutes\");\n            const hours = loggedDurationMinutes >= 60 ? Math.floor(loggedDurationMinutes / 60) : 0;\n            const minutes = loggedDurationMinutes % 60;\n            const reportItem = {\n                crewID: +item.crewMember.id,\n                crewName: \"\".concat(item.crewMember.firstName, \" \").concat(item.crewMember.surname),\n                totalLoggedMinutes: loggedDurationMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                },\n                loginTime: new Date(item.punchIn),\n                logoutTime: new Date(item.punchOut),\n                dutyPerformedID: +item.dutyPerformedID,\n                primaryDuty: item.dutyPerformed.title,\n                vesselID: +item.logBookEntry.vehicleID,\n                vesselName: item.logBookEntry.vehicle.title,\n                workDetails: item.workDetails\n            };\n            return reportItem;\n        });\n        console.log(\"\\uD83D\\uDCCB Report items created:\", reportItems.length);\n        console.log(\"\\uD83D\\uDCCB Report items sample:\", reportItems.slice(0, 2));\n        console.log(\"\\uD83C\\uDFAF Report mode:\", reportMode);\n        if (reportMode === \"detailed\") {\n            console.log(\"✅ Returning detailed report with\", reportItems.length, \"items\");\n            return reportItems;\n        }\n        //create combined id string from crewID, dutyID, vesselID\n        const combinedIDs = reportItems.map((item)=>\"\".concat(item.crewID, \"|\").concat(item.dutyPerformedID, \"|\").concat(item.vesselID));\n        const summarizedReportItems = [];\n        new Set(combinedIDs).forEach((value)=>{\n            const [crewID, dutyPerformedID, vesselID] = value.split(\"|\");\n            const relatedReportItems = reportItems.filter((value)=>{\n                return value.crewID === +crewID && value.dutyPerformedID === +dutyPerformedID && value.vesselID === +vesselID;\n            });\n            const totalLoggedMinutes = relatedReportItems.reduce((prev, current)=>prev + current.totalLoggedMinutes, 0);\n            const singleRelatedReportItem = relatedReportItems[0];\n            const hours = totalLoggedMinutes >= 60 ? Math.floor(totalLoggedMinutes / 60) : 0;\n            const minutes = totalLoggedMinutes % 60;\n            const item = {\n                ...singleRelatedReportItem,\n                loginTime: dateRange.startDate,\n                logoutTime: dateRange.endDate,\n                totalLoggedMinutes,\n                loggedDuration: {\n                    hours,\n                    minutes\n                }\n            };\n            summarizedReportItems.push(item);\n        });\n        console.log(\"✅ Returning summarized report with\", summarizedReportItems.length, \"items\");\n        return summarizedReportItems;\n    }, [\n        called,\n        data,\n        loading,\n        reportMode\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"crew\",\n            \"vessel\",\n            \"duty\",\n            \"signed in\",\n            \"signed out\",\n            \"time spent\"\n        ]);\n        reportData.forEach((item)=>{\n            csvEntries.push([\n                item.crewName,\n                item.vesselName,\n                item.primaryDuty,\n                item.loginTime.toISOString(),\n                item.logoutTime.toISOString(),\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_5__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const headers = [\n            [\n                \"Crew\",\n                \"Vessel\",\n                \"Duty\",\n                \"Signed in\",\n                \"Signed out\",\n                \"Time spent\"\n            ]\n        ];\n        const data = reportData.map(function(item) {\n            return [\n                item.crewName + \"\",\n                item.vesselName + \"\",\n                item.primaryDuty + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.loginTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.logoutTime).format(\"DD/MM/YY HH:mm\") + \"\",\n                \"\".concat(item.loggedDuration.hours > 0 ? \"\".concat(item.loggedDuration.hours, \"h \") : \"\").concat(item.loggedDuration.minutes, \"m\")\n            ];\n        });\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_6__.exportPdfTable)({\n            headers,\n            body: data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.ListHeader, {\n                title: \"Crew Seatime Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 378,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                    className: \"flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            onChange: handleFilterOnChange,\n                            onClick: generateReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_export_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            onDownloadPdf: downloadPdf,\n                            onDownloadCsv: downloadCsv\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Crew member name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Vessel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Duty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Signed out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableHead, {\n                                                children: \"Time spent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                        isLoading: called && loading,\n                                        reportData: reportData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 390,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewCrewSeatimeReport, \"yNZW2+JNzpS40rCLUUbQ1DyQT3A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c = NewCrewSeatimeReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 434,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 433,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                colSpan: 6,\n                className: \"text-center  h-32\",\n                children: \"No Data Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                lineNumber: 444,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 443,\n            columnNumber: 13\n        }, this);\n    }\n    return reportData.map((element, index)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableRow, {\n            className: \"group border-b  hover: \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block ml-3\",\n                        children: element.crewName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: element.primaryDuty\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.loginTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: dayjs__WEBPACK_IMPORTED_MODULE_4___default()(element.logoutTime).format(\"DD/M/YY HH:mm\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_9__.TableCell, {\n                    className: \"px-2 py-3 text-left\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" inline-block \",\n                        children: [\n                            element.loggedDuration.hours != 0 ? \"\".concat(element.loggedDuration.hours, \"h, \") : \"\",\n                            element.loggedDuration.minutes,\n                            \"m\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, \"\".concat(element.crewID, \"-\").concat(index), true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\new-crew-seatime-report.tsx\",\n            lineNumber: 453,\n            columnNumber: 13\n        }, this);\n    });\n}\n_c1 = TableContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"NewCrewSeatimeReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/new-crew-seatime-report.tsx\n"));

/***/ })

});